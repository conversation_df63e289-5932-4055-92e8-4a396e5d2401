package api

import (
	"bytes"
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"billing_service/app"
	"billing_service/repo"
	"billing_service/util/config"
	"billing_service/util/logger"

	"github.com/gofiber/fiber/v3"
	"github.com/stretchr/testify/assert"
)

func TestDriverTax(t *testing.T) {
	// Create a test configuration
	cfg := &config.Config{}
	log := logger.Get("debug")

	// Create a mock context
	ctx := context.Background()

	// Create test app and handler
	testRepo := repo.Get(ctx, cfg, log)
	testApp := app.Get(ctx)
	handler := &handler{
		ctx:  ctx,
		repo: testRepo,
		app:  testApp,
	}

	// Create fiber app
	app := fiber.New()
	app.Post("/v1/drivers/:driver_id/tax", handler.DriverTax)

	tests := []struct {
		name           string
		driverId       string
		requestBody    DriverTaxRequest
		expectedStatus int
		expectedError  string
	}{
		{
			name:     "Valid driver tax request",
			driverId: "123",
			requestBody: DriverTaxRequest{
				MytaxiId: 11221121111,
				RideId:   10143413,
				Amount:   20000,
				Comment:  "С вас была снята сумма 20 000 сум для расчета налога",
			},
			expectedStatus: 200,
		},
		{
			name:     "Invalid driver_id",
			driverId: "0",
			requestBody: DriverTaxRequest{
				MytaxiId: 11221121111,
				RideId:   10143413,
				Amount:   20000,
				Comment:  "Test comment",
			},
			expectedStatus: 400,
			expectedError:  "incorrect driver_id",
		},
		{
			name:     "Missing mytaxi_id",
			driverId: "123",
			requestBody: DriverTaxRequest{
				RideId:  10143413,
				Amount:  20000,
				Comment: "Test comment",
			},
			expectedStatus: 400,
			expectedError:  "mytaxi_id is required",
		},
		{
			name:     "Missing ride_id",
			driverId: "123",
			requestBody: DriverTaxRequest{
				MytaxiId: 11221121111,
				Amount:   20000,
				Comment:  "Test comment",
			},
			expectedStatus: 400,
			expectedError:  "ride_id is required",
		},
		{
			name:     "Invalid amount",
			driverId: "123",
			requestBody: DriverTaxRequest{
				MytaxiId: 11221121111,
				RideId:   10143413,
				Amount:   0,
				Comment:  "Test comment",
			},
			expectedStatus: 400,
			expectedError:  "amount must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Marshal request body
			body, err := json.Marshal(tt.requestBody)
			assert.NoError(t, err)

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/v1/drivers/"+tt.driverId+"/tax", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")

			// Perform request
			resp, err := app.Test(req)
			assert.NoError(t, err)

			// Check status code
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			// If we expect an error, check the response body
			if tt.expectedError != "" {
				var errorResp map[string]interface{}
				err = json.NewDecoder(resp.Body).Decode(&errorResp)
				assert.NoError(t, err)

				errorBody, ok := errorResp["error"].(map[string]interface{})
				assert.True(t, ok)
				assert.Contains(t, errorBody["message"], tt.expectedError)
			}
		})
	}
}
